:root {
  --transition-duration: 0.3s;
  --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth transitions for theme changes */
* {
  transition: background-color var(--transition-duration) var(--transition-timing),
              color var(--transition-duration) var(--transition-timing),
              border-color var(--transition-duration) var(--transition-timing),
              box-shadow var(--transition-duration) var(--transition-timing);
}

/* Base styles with improved typography */
body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  min-height: 100vh;
  overflow-x: hidden;
  text-rendering: optimizeLegibility;
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(128, 128, 128, 0.5);
  border-radius: 5px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(128, 128, 128, 0.7);
}

/* Smooth focus states */
*:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(103, 58, 183, 0.5);
  transition: box-shadow 0.2s ease;
}

/* Animation classes */
.fade-in {
  animation: fadeIn var(--transition-duration) var(--transition-timing);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.4s var(--transition-timing);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s var(--transition-timing);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Card hover effects */
.card-hover {
  transition: transform 0.3s var(--transition-timing),
              box-shadow 0.3s var(--transition-timing);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.w-100 { width: 100%; }
.h-100 { height: 100%; }
.mb-2 { margin-bottom: 1rem; }
.mt-2 { margin-top: 1rem; }
.p-2 { padding: 1rem; }

/* Form styles */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

/* Mobile optimizations */
@media (max-width: 600px) {
  .hide-on-mobile {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  .mobile-text-center {
    text-align: center !important;
  }
}

/* Better focus styles for accessibility */
a:focus, button:focus, input:focus, select:focus, textarea:focus {
  outline: 2px solid rgba(103, 58, 183, 0.5);
  outline-offset: 2px;
}

/* Enhance touch targets for mobile */
@media (max-width: 600px) {
  button, 
  .MuiButtonBase-root {
    min-height: 48px;
  }
  
  .MuiInputBase-input {
    padding: 12px !important;
  }
}
