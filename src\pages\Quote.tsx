import React, { useState } from 'react';
import { Box, Button, Typography, TextField, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import { PDFDownloadLink, Document, Page, Text, View, Image, StyleSheet } from '@react-pdf/renderer';

interface QuoteItem {
  itemName: string;
  price: number;
  quantity: number;
  discount: number;
}

const styles = StyleSheet.create({
  page: {
    padding: 30,
  },
  header: {
    marginBottom: 20,
    alignItems: 'center',
  },
  logo: {
    width: 150,
    marginBottom: 10,
  },
  title: {
    fontSize: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  table: {
    width: '100%',
    marginBottom: 20,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#000',
    borderBottomStyle: 'solid',
    paddingVertical: 5,
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
  },
  tableCell: {
    flex: 1,
    padding: 5,
  },
  total: {
    marginTop: 20,
    textAlign: 'right',
    fontSize: 18,
  },
});

const QuotePDF = ({ items }: { items: QuoteItem[] }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <View style={styles.header}>
        <Image src="/Dhishank Final With Compass.png" style={styles.logo} />
        <Text style={styles.title}>Quote</Text>
      </View>
      <View style={styles.table}>
        <View style={[styles.tableRow, styles.tableHeader]}>
          <Text style={styles.tableCell}>Item</Text>
          <Text style={styles.tableCell}>Price</Text>
          <Text style={styles.tableCell}>Quantity</Text>
          <Text style={styles.tableCell}>Discount</Text>
          <Text style={styles.tableCell}>Total</Text>
        </View>
        {items.map((item, index) => (
          <View key={index} style={styles.tableRow}>
            <Text style={styles.tableCell}>{item.itemName}</Text>
            <Text style={styles.tableCell}>${item.price}</Text>
            <Text style={styles.tableCell}>{item.quantity}</Text>
            <Text style={styles.tableCell}>{item.discount}%</Text>
            <Text style={styles.tableCell}>
              ${((item.price * item.quantity) * (1 - item.discount / 100)).toFixed(2)}
            </Text>
          </View>
        ))}
      </View>
      <Text style={styles.total}>
        Total: ${items.reduce((sum, item) => 
          sum + (item.price * item.quantity * (1 - item.discount / 100)), 0
        ).toFixed(2)}
      </Text>
    </Page>
  </Document>
);

const Quote = () => {
  const [items, setItems] = useState<QuoteItem[]>([]);
  const [currentItem, setCurrentItem] = useState<QuoteItem>({
    itemName: '',
    price: 0,
    quantity: 1,
    discount: 0,
  });

  const handleAddItem = () => {
    if (currentItem.itemName && currentItem.price > 0) {
      setItems([...items, currentItem]);
      setCurrentItem({
        itemName: '',
        price: 0,
        quantity: 1,
        discount: 0,
      });
    }
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Create Quote
      </Typography>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" gap={2} mb={2}>
          <TextField
            label="Item Name"
            value={currentItem.itemName}
            onChange={(e) => setCurrentItem({ ...currentItem, itemName: e.target.value })}
          />
          <TextField
            label="Price"
            type="number"
            value={currentItem.price}
            onChange={(e) => setCurrentItem({ ...currentItem, price: parseFloat(e.target.value) })}
          />
          <TextField
            label="Quantity"
            type="number"
            value={currentItem.quantity}
            onChange={(e) => setCurrentItem({ ...currentItem, quantity: parseInt(e.target.value) })}
          />
          <TextField
            label="Discount (%)"
            type="number"
            value={currentItem.discount}
            onChange={(e) => setCurrentItem({ ...currentItem, discount: parseFloat(e.target.value) })}
          />
          <Button variant="contained" onClick={handleAddItem}>
            Add Item
          </Button>
        </Box>
      </Paper>

      {items.length > 0 && (
        <>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Item</TableCell>
                  <TableCell>Price</TableCell>
                  <TableCell>Quantity</TableCell>
                  <TableCell>Discount</TableCell>
                  <TableCell>Total</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.itemName}</TableCell>
                    <TableCell>${item.price}</TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{item.discount}%</TableCell>
                    <TableCell>
                      ${((item.price * item.quantity) * (1 - item.discount / 100)).toFixed(2)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box mt={2} display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Total: ${items.reduce((sum, item) => 
                sum + (item.price * item.quantity * (1 - item.discount / 100)), 0
              ).toFixed(2)}
            </Typography>
            <PDFDownloadLink
              document={<QuotePDF items={items} />}
              fileName="quote.pdf"
            >
              {({ loading }: { loading: boolean }) => (
                <Button variant="contained" disabled={loading}>
                  {loading ? 'Generating PDF...' : 'Download Quote PDF'}
                </Button>
              )}
            </PDFDownloadLink>
          </Box>
        </>
      )}
    </Box>
  );
};

export default Quote;