{"name": "employee-site", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fontsource/inter": "^5.2.5", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@supabase/supabase-js": "^2.39.7", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/jspdf": "^1.3.3", "@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^2.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}