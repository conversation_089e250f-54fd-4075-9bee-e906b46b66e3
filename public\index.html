<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#673ab7" />
    <meta name="description" content="Dhishank Employee Portal - Manage sales, expenses and inventory efficiently" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Dhishank Employee Portal" />

    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- Preconnect to important domains -->
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <title>Dhishank Employee Portal</title>

    <!-- Critical CSS can go here -->
    <style>
      body {
        margin: 0;
        padding: 0;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      #root {
        min-height: 100vh;
      }

      .initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f9fafb;
        z-index: 9999;
      }

      @media (prefers-color-scheme: dark) {
        .initial-loader {
          background-color: #121212;
        }
      }

      .loader-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(103, 58, 183, 0.3);
        border-radius: 50%;
        border-top-color: #673ab7;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <!-- Initial loading state before React loads -->
      <div class="initial-loader">
        <div class="loader-spinner"></div>
      </div>
    </div>
  </body>
</html>
